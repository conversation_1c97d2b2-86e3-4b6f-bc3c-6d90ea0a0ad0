"use client";

import { useState } from "react";
import Image from "next/image";
import { ChevronDown, ChevronLeft, Expand, Heart, MoreHorizontal, Share, X } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { Link } from "@/i18n/navigation";

// Client component that renders the property detail UI
export default function PropertyDetailClient({ property }: { property: any }) {
  const [showMore, setShowMore] = useState(false);
  const router = useRouter();

  // Extract property data with fallbacks
  const propertyData = {
    name: property.name || "",
    price: property.price || 0,
    address: property.address || "",
    description: property.description || "",
    rooms: property.rooms || 0,
    toilets: property.toilets || 0,
    area: property.area || 0,
    propertyType: property.propertyType || "",
    yearBuilt: property.yearBuilt || "--",
    status: property.status || "unknown",
    createdAt: property.createdAt || new Date().toISOString(),
    updatedAt: property.updatedAt || new Date().toISOString(),
    owner: property.owner || {},
    images: property.propertyMedia?.map((pm: any) => pm.mediaURL) || []
  };

  // Handle back button click
  const handleBackClick = () => {
    router.back();
  };

  return (
    <div className="max-w-6xl mx-auto bg-white">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-white p-4 border-b flex items-center justify-between">
        <button onClick={handleBackClick} className="flex items-center text-gray-600">
          <ChevronLeft className="h-5 w-5 mr-2" />
          <span>Quay lại</span>
        </button>
        <div className="flex-1 flex justify-center">
          <Image src="/yezhome_logo.png" alt="YEZ Home" width={120} height={40} className="h-8" />
        </div>
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-1 text-gray-700">
            <Heart className="h-5 w-5" />
            <span className="hidden sm:inline">Lưu</span>
          </button>
          <button className="flex items-center gap-1 text-gray-700">
            <Share className="h-5 w-5" />
            <span className="hidden sm:inline">Chia sẻ</span>
          </button>
          <button onClick={handleBackClick} className="flex items-center gap-1 text-gray-700">
            <X className="h-5 w-5" />
            <span className="hidden sm:inline">Đóng</span>
          </button>
        </div>
      </header>

      {/* Property Images */}
      <div className="relative grid grid-cols-1 md:grid-cols-3 gap-2 p-4">
        <div className="relative md:col-span-2 h-80 md:h-96 rounded-md overflow-hidden">
          <div className="absolute top-2 left-2 z-10 bg-yellow-400 text-gray-800 px-2 py-1 rounded-md text-xs font-medium">
            {propertyData.status === "active" ? "Đang bán" : "Đã bán"}
          </div>
          <Image
            src={propertyData.images[0] || "/placeholder.svg?height=400&width=600"}
            alt={propertyData.name}
            fill
            className="object-cover"
          />
        </div>
        <div className="hidden md:grid grid-rows-4 gap-2 h-96">
          {propertyData.images.length > 1 ? (
            <>
              <div className="row-span-2 relative rounded-md overflow-hidden">
                <Image
                  src={propertyData.images[1] || "/placeholder.svg?height=200&width=300"}
                  alt={`${propertyData.name} image 2`}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="relative rounded-md overflow-hidden">
                <Image
                  src={propertyData.images[2] || "/placeholder.svg?height=100&width=300"}
                  alt={`${propertyData.name} image 3`}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="relative rounded-md overflow-hidden">
                <Image
                  src={propertyData.images[3] || "/placeholder.svg?height=100&width=300"}
                  alt={`${propertyData.name} image 4`}
                  fill
                  className="object-cover"
                />
                <button className="absolute bottom-2 right-2 bg-white bg-opacity-90 text-gray-800 p-2 rounded-md flex items-center gap-1 text-xs font-medium">
                  <Expand className="h-4 w-4" />
                  Xem tất cả {propertyData.images.length} ảnh
                </button>
              </div>
            </>
          ) : (
            <div className="row-span-4 relative rounded-md overflow-hidden bg-gray-100 flex items-center justify-center">
              <p className="text-gray-500 text-sm">Không có ảnh bổ sung</p>
            </div>
          )}
        </div>
      </div>

      {/* Property Details */}
      <div className="p-4 grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          {/* Price and Address */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">{formatCurrency(propertyData.price)}</h1>
            <p className="text-lg text-gray-700">{propertyData.address}</p>
          </div>

          {/* Key Details */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-6">
            <div className="bg-gray-100 p-4 rounded-md flex flex-col items-center justify-center">
              <span className="text-2xl font-bold">{propertyData.rooms}</span>
              <span className="text-gray-600">phòng ngủ</span>
            </div>
            <div className="bg-gray-100 p-4 rounded-md flex flex-col items-center justify-center">
              <span className="text-2xl font-bold">{propertyData.toilets}</span>
              <span className="text-gray-600">phòng tắm</span>
            </div>
            <div className="bg-gray-100 p-4 rounded-md flex flex-col items-center justify-center">
              <span className="text-2xl font-bold">{propertyData.area}</span>
              <span className="text-gray-600">m²</span>
            </div>
          </div>

          {/* Property Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-6">
            <div className="bg-gray-100 p-3 rounded-md flex items-center">
              <svg
                className="h-5 w-5 mr-2 text-gray-600"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <polyline points="9 22 9 12 15 12 15 22" />
              </svg>
              <span className="text-gray-700">{propertyData.propertyType}</span>
            </div>
            <div className="bg-gray-100 p-3 rounded-md flex items-center">
              <svg
                className="h-5 w-5 mr-2 text-gray-600"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                <line x1="16" y1="2" x2="16" y2="6" />
                <line x1="8" y1="2" x2="8" y2="6" />
                <line x1="3" y1="10" x2="21" y2="10" />
              </svg>
              <span className="text-gray-700">Năm xây dựng: {propertyData.yearBuilt}</span>
            </div>
            <div className="bg-gray-100 p-3 rounded-md flex items-center">
              <svg
                className="h-5 w-5 mr-2 text-gray-600"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" />
                <circle cx="12" cy="10" r="3" />
              </svg>
              <span className="text-gray-700">{propertyData.area} m² đất</span>
            </div>
          </div>

          <hr className="my-6" />

          {/* What's Special */}
          <div className="mb-6">
            <h2 className="text-2xl font-bold mb-4">Mô tả</h2>
            <div className="text-gray-700">
              <p className="mb-4">
                {showMore ? propertyData.description : `${propertyData.description.substring(0, 300)}${propertyData.description.length > 300 ? '...' : ''}`}
              </p>
              {propertyData.description.length > 300 && (
                <button onClick={() => setShowMore(!showMore)} className="text-blue-600 flex items-center">
                  {showMore ? (
                    <>
                      Ẩn bớt <ChevronDown className="h-4 w-4 ml-1 transform rotate-180" />
                    </>
                  ) : (
                    <>
                      Xem thêm <ChevronDown className="h-4 w-4 ml-1" />
                    </>
                  )}
                </button>
              )}
            </div>
          </div>

          {/* Listing Details */}
          <div className="mb-6">
            <div className="mt-4 text-sm text-gray-700">
              <p>Cập nhật lần cuối: {new Date(propertyData.updatedAt).toLocaleDateString('vi-VN')}</p>
              <p>Ngày đăng: {new Date(propertyData.createdAt).toLocaleDateString('vi-VN')}</p>
              <p className="mt-2">Người đăng: {propertyData.owner.fullName || 'Không có thông tin'}</p>
            </div>
          </div>

          {/* Map */}
          <div className="relative h-64 rounded-md overflow-hidden mb-6 bg-gray-100">
            <div className="absolute inset-0 flex items-center justify-center">
              <p className="text-gray-500">Bản đồ đang được tải...</p>
            </div>
            <div className="absolute top-2 left-2 bg-white px-2 py-1 rounded-md text-xs font-medium">Xem đường</div>
            <button className="absolute top-2 right-2 bg-white p-1 rounded-md">
              <Expand className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Contact Section */}
        <div className="lg:col-span-1">
          <div className="bg-white border rounded-md p-4 sticky top-20">
            <button className="w-full bg-blue-600 text-white font-semibold py-3 px-4 rounded-md mb-3">
              Yêu cầu xem nhà
            </button>
            <p className="text-center text-sm text-gray-600 mb-4">Có thể xem ngay hôm nay</p>
            <button className="w-full bg-white border border-blue-600 text-blue-600 font-semibold py-3 px-4 rounded-md">
              Liên hệ người bán
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}