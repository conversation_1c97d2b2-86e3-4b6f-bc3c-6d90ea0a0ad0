{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/favorite/loading.jsx"], "sourcesContent": ["import { Loader2 } from \"lucide-react\";\n\nexport default function Loading() {\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      <div className=\"mb-6\">\n        <div className=\"h-8 bg-gray-200 rounded w-64 mb-2 animate-pulse\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-96 animate-pulse\"></div>\n      </div>\n      \n      <div className=\"flex items-center justify-center py-12\">\n        <Loader2 className=\"h-8 w-8 animate-spin text-teal-500\" />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}]}