namespace RealEstate.Application.DTO
{
    public class NotificationDto
    {
        public Guid Id { get; set; }
        public Guid? UserId { get; set; }
        public string Type { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public bool IsRead { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid? RelatedEntityId { get; set; } // Contact request ID, transaction ID, etc.
        public Guid? RelatedPropertyId { get; set; } // Property ID for property-related notifications
        public string? ActionUrl { get; set; } // URL for direct navigation
    }
} 