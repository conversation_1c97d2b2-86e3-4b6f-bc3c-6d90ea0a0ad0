(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/react-multi-carousel/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-multi-carousel_30f4bf3b._.js",
  "static/chunks/node_modules_react-multi-carousel_index_1bf2721f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-multi-carousel/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);