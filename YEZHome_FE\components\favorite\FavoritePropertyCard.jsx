"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Heart, MapPin, Square, BedDouble, Bath, Calendar, Trash2 } from "lucide-react";
import Image from "next/image";
import { Link } from "@/i18n/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function FavoritePropertyCard({ favorite, onRemove }) {
  const t = useTranslations("FavoritePropertyCard");
  const [isRemoving, setIsRemoving] = useState(false);

  const { property } = favorite;

  const handleRemove = async () => {
    setIsRemoving(true);
    try {
      await onRemove(property.id);
    } finally {
      setIsRemoving(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <div className="relative">
        <Link href={`/bds/${property.id}`}>
          <Image
            src={property.propertyMedia?.[0]?.mediaURL || "/placeholder.svg"}
            alt={property.name}
            width={400}
            height={200}
            className="w-full h-48 object-cover hover:scale-105 transition-transform duration-300"
            loading="lazy"
          />
        </Link>
        
        {/* Property Type Badge */}
        <div className="absolute top-2 left-2">
          <Badge variant="secondary" className="bg-teal-500 text-white">
            {property.postType === "sell" ? t("forSale", { defaultValue: "Bán" }) : t("forRent", { defaultValue: "Cho thuê" })}
          </Badge>
        </div>

        {/* Remove Button */}
        <div className="absolute top-2 right-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="destructive"
                size="sm"
                className="h-8 w-8 p-0 bg-red-500 hover:bg-red-600"
                disabled={isRemoving}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>
                  {t("confirmRemoveTitle", { defaultValue: "Xóa khỏi danh sách yêu thích?" })}
                </AlertDialogTitle>
                <AlertDialogDescription>
                  {t("confirmRemoveDescription", { 
                    defaultValue: "Bạn có chắc chắn muốn xóa bất động sản này khỏi danh sách yêu thích không?" 
                  })}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>
                  {t("cancel", { defaultValue: "Hủy" })}
                </AlertDialogCancel>
                <AlertDialogAction onClick={handleRemove} disabled={isRemoving}>
                  {isRemoving ? t("removing", { defaultValue: "Đang xóa..." }) : t("remove", { defaultValue: "Xóa" })}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Property Name */}
          <Link href={`/bds/${property.id}`}>
            <h3 className="font-semibold line-clamp-2 hover:text-teal-600 transition-colors">
              {property.name}
            </h3>
          </Link>

          {/* Price */}
          <div className="text-xl font-bold text-teal-600">
            {formatCurrency(property.price)}
            {property.postType === "rent" && (
              <span className="text-sm font-normal text-gray-500">/{t("month", { defaultValue: "tháng" })}</span>
            )}
          </div>

          {/* Address */}
          <div className="flex items-center text-gray-600 text-xs">
            <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
            <span className="line-clamp-1">{property.addressSelected || property.address}</span>
          </div>

          {/* Property Details */}
          <div className="flex justify-between text-sm text-gray-500">
            <div className="flex items-center">
              <Square className="h-3 w-3 mr-1" />
              <span className="text-xs">{property.area || "__"} m²</span>
            </div>
            <div className="flex items-center">
              <BedDouble className="h-3 w-3 mr-1" />
              <span className="text-xs">{property.rooms || "__"} {t("bedrooms", { defaultValue: "PN" })}</span>
            </div>
            <div className="flex items-center">
              <Bath className="h-3 w-3 mr-1" />
              <span className="text-xs">{property.toilets || "__"} {t("bathrooms", { defaultValue: "WC" })}</span>
            </div>
          </div>

          {/* Added Date */}
          <div className="flex items-center text-xs text-gray-400 pt-2 border-t">
            <Calendar className="h-3 w-3 mr-1" />
            <span>
              {t("addedOn", { defaultValue: "Đã thêm vào" })} {formatDate(favorite.createdAt)}
            </span>
          </div>

          {/* Action Button */}
          <Button
            asChild
            className="w-full bg-teal-500 hover:bg-teal-600 text-white"
            size="sm"
          >
            <Link href={`/bds/${property.id}`}>
              {t("viewDetails", { defaultValue: "Xem chi tiết" })}
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
