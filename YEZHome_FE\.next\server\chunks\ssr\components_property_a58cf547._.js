module.exports = {

"[project]/components/property/NearbyPropertiesCarousel.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_b25b36b0._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/NearbyPropertiesCarousel.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/components/property/PropertyImageGallery.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_fe004f2c._.js",
  "server/chunks/ssr/components_a9be04cc._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/PropertyImageGallery.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/components/property/PropertyDescription.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/components_89faa978._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/PropertyDescription.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),

};